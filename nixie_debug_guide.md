# 数码管调试指南

## 🔍 数码管不亮的常见原因及解决方案

### 1. 硬件连接问题

#### 检查项目：
- ✅ **电源连接**：确认数码管VCC和GND正确连接
- ✅ **引脚连接**：验证所有引脚连接正确
- ✅ **数码管类型**：确认是共阴极还是共阳极

#### 解决方案：
```
1. 用万用表测量数码管电源电压
2. 检查引脚连接是否松动
3. 确认数码管型号和驱动方式
```

### 2. 代码逻辑问题

#### 常见错误：
- ❌ **段码表错误**：共阴极/共阳极段码不匹配
- ❌ **扫描频率过低**：刷新太慢导致闪烁或不亮
- ❌ **时钟分频错误**：分频逻辑有误

#### 解决方案：
使用简化测试代码验证硬件：

```verilog
// 使用提供的 nixie_test.v 进行测试
// 该模块显示固定数字 "12345678"
```

### 3. 约束文件问题

#### 检查项目：
- ✅ **引脚分配**：确认引脚号与硬件一致
- ✅ **电平标准**：IOSTANDARD设置正确
- ✅ **约束语法**：无语法错误

#### 验证方法：
```tcl
# 在Vivado中检查约束
report_property [get_ports nixie_segment[*]]
report_property [get_ports nixie_enable[*]]
```

## 🛠️ 调试步骤

### 第一步：硬件基础测试

1. **LED测试**：
   - 先确认LED能正常工作
   - 验证FPGA下载和基本功能

2. **简单输出测试**：
   ```verilog
   // 让所有数码管段全亮
   nixie_enable <= 8'b11111110;  // 只点亮第一个数码管
   nixie_segment <= 8'b10000000; // 全段点亮(共阴极)
   ```

### 第二步：使用测试模块

1. **下载测试代码**：
   ```tcl
   # 创建测试项目
   create_project nixie_test ./test_project -part xc7a100tfgg484-1 -force
   add_files -norecurse {nixie_test.v}
   add_files -fileset constrs_1 -norecurse {nixie_test.xdc}
   set_property top nixie_test_top [current_fileset]
   ```

2. **观察现象**：
   - 应该显示 "12345678"
   - 黄色LED每秒闪烁一次
   - 绿色LED显示流水灯效果

### 第三步：逐步调试

#### 如果测试模块也不工作：

1. **检查时钟**：
   ```verilog
   // 添加时钟指示
   reg [26:0] clk_counter;
   always @(posedge clk) begin
       clk_counter <= clk_counter + 1;
   end
   assign yellow_led[0] = clk_counter[26]; // 约1.3秒闪烁
   ```

2. **检查复位**：
   ```verilog
   // 确认复位信号正常
   assign red_led[0] = reset;
   ```

3. **静态显示测试**：
   ```verilog
   // 固定显示一个数字
   always @(*) begin
       nixie_enable = 8'b11111110;   // 只选择第一个数码管
       nixie_segment = 8'b11111001;  // 显示数字1
   end
   ```

## 🔧 常见问题解决

### 问题1：数码管全不亮
**可能原因**：
- 电源问题
- 所有引脚约束错误
- 时钟不工作

**解决方案**：
1. 检查LED是否正常（验证FPGA工作）
2. 用示波器检查时钟信号
3. 验证电源电压

### 问题2：数码管闪烁严重
**可能原因**：
- 扫描频率太低
- 时钟分频错误

**解决方案**：
```verilog
// 提高扫描频率
if (counter[12:0] == 13'd0) begin  // 更快的扫描
    // 扫描逻辑
end
```

### 问题3：显示数字错误
**可能原因**：
- 段码表定义错误
- 引脚分配错误

**解决方案**：
1. 验证段码表与数码管类型匹配
2. 逐个测试每个段的显示

### 问题4：只有部分数码管亮
**可能原因**：
- 位选信号问题
- 部分引脚连接错误

**解决方案**：
```verilog
// 逐个测试每个数码管
nixie_enable <= 8'b11111110;  // 测试第0个
nixie_enable <= 8'b11111101;  // 测试第1个
// ... 依次测试
```

## 📋 测试检查清单

### 硬件检查：
- [ ] 数码管电源连接正确
- [ ] 所有引脚连接牢固
- [ ] 数码管型号确认（共阴极/共阳极）
- [ ] 电压电平匹配（3.3V）

### 软件检查：
- [ ] 约束文件引脚分配正确
- [ ] 段码表与数码管类型匹配
- [ ] 时钟分频逻辑正确
- [ ] 扫描频率适当（1-4ms）

### 功能检查：
- [ ] LED正常工作（验证FPGA基本功能）
- [ ] 时钟信号正常
- [ ] 复位信号正常
- [ ] 测试模块能显示固定数字

## 🎯 推荐调试流程

1. **使用nixie_test.v测试基本显示功能**
2. **确认能显示"12345678"后再使用完整系统**
3. **逐步增加功能复杂度**
4. **使用ILA观察内部信号**

如果测试模块正常工作，说明硬件连接正确，问题在于原始代码的逻辑。
如果测试模块也不工作，说明硬件连接或约束文件有问题。
