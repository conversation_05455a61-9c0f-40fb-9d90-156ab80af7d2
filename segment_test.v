`timescale 1ns / 1ps

//////////////////////////////////////////////////////////////////////////////////
// 数码管段测试模块
// 功能：逐个测试每个段，确定正确的段码表
//////////////////////////////////////////////////////////////////////////////////

module segment_test(
    input wire clk,                    // 100MHz时钟
    input wire reset,                  // 复位信号
    
    // 数码管输出
    output reg [7:0] nixie_enable,     // 数码管位选 A7-A0 (低电平有效)
    output reg [7:0] nixie_segment     // 数码管段选 CA-CG,DP (低电平有效)
);

    // 时钟分频器
    reg [26:0] counter;                // 分频计数器
    reg [3:0] test_state;              // 测试状态
    
    // 测试序列
    always @(posedge clk) begin
        if (reset) begin
            counter <= 27'd0;
            test_state <= 4'd0;
            nixie_enable <= 8'hFF;     // 全部关闭
            nixie_segment <= 8'hFF;    // 全部关闭
        end else begin
            counter <= counter + 1;
            
            // 每2秒切换一次测试状态
            if (counter >= 27'd199_999_999) begin
                counter <= 27'd0;
                test_state <= test_state + 1;
                if (test_state >= 4'd10) begin
                    test_state <= 4'd0;
                end
            end
            
            // 只点亮第一个数码管
            nixie_enable <= 8'b11111110;
            
            // 根据测试状态输出不同的段码
            case (test_state)
                4'd0: nixie_segment <= 8'b01111111; // 测试CA段 (bit 7)
                4'd1: nixie_segment <= 8'b10111111; // 测试CB段 (bit 6)
                4'd2: nixie_segment <= 8'b11011111; // 测试CC段 (bit 5)
                4'd3: nixie_segment <= 8'b11101111; // 测试CD段 (bit 4)
                4'd4: nixie_segment <= 8'b11110111; // 测试CE段 (bit 3)
                4'd5: nixie_segment <= 8'b11111011; // 测试CF段 (bit 2)
                4'd6: nixie_segment <= 8'b11111101; // 测试CG段 (bit 1)
                4'd7: nixie_segment <= 8'b11111110; // 测试DP段 (bit 0)
                4'd8: nixie_segment <= 8'b00000000; // 全段亮
                4'd9: nixie_segment <= 8'b11111111; // 全段灭
                default: nixie_segment <= 8'b11111111;
            endcase
        end
    end

endmodule

//////////////////////////////////////////////////////////////////////////////////
// 段测试顶层模块
//////////////////////////////////////////////////////////////////////////////////

module segment_test_top(
    input wire clk,                    // 100MHz时钟 Y18
    input wire reset,                  // 复位信号 P20
    
    // 数码管输出
    output wire [7:0] nixie_enable,    // 数码管位选 A7-A0
    output wire [7:0] nixie_segment,   // 数码管段选 CA-CG,DP
    
    // LED输出 (显示当前测试状态)
    output reg [7:0] red_led,          // 红色LED
    output reg [7:0] green_led,        // 绿色LED  
    output reg [7:0] yellow_led        // 黄色LED
);

    // 实例化段测试模块
    segment_test seg_test_inst(
        .clk(clk),
        .reset(reset),
        .nixie_enable(nixie_enable),
        .nixie_segment(nixie_segment)
    );
    
    // LED指示当前测试状态
    reg [26:0] led_counter;
    reg [3:0] current_test_state;
    
    always @(posedge clk) begin
        if (reset) begin
            led_counter <= 27'd0;
            current_test_state <= 4'd0;
            red_led <= 8'h00;
            green_led <= 8'h00;
            yellow_led <= 8'h00;
        end else begin
            led_counter <= led_counter + 1;
            
            // 跟踪测试状态
            if (led_counter >= 27'd199_999_999) begin
                led_counter <= 27'd0;
                current_test_state <= current_test_state + 1;
                if (current_test_state >= 4'd10) begin
                    current_test_state <= 4'd0;
                end
            end
            
            // LED显示当前测试的段
            case (current_test_state)
                4'd0: begin // 测试CA段
                    red_led <= 8'b00000001;
                    green_led <= 8'h00;
                    yellow_led <= 8'h00;
                end
                4'd1: begin // 测试CB段
                    red_led <= 8'b00000010;
                    green_led <= 8'h00;
                    yellow_led <= 8'h00;
                end
                4'd2: begin // 测试CC段
                    red_led <= 8'b00000100;
                    green_led <= 8'h00;
                    yellow_led <= 8'h00;
                end
                4'd3: begin // 测试CD段
                    red_led <= 8'b00001000;
                    green_led <= 8'h00;
                    yellow_led <= 8'h00;
                end
                4'd4: begin // 测试CE段
                    red_led <= 8'h00;
                    green_led <= 8'b00000001;
                    yellow_led <= 8'h00;
                end
                4'd5: begin // 测试CF段
                    red_led <= 8'h00;
                    green_led <= 8'b00000010;
                    yellow_led <= 8'h00;
                end
                4'd6: begin // 测试CG段
                    red_led <= 8'h00;
                    green_led <= 8'b00000100;
                    yellow_led <= 8'h00;
                end
                4'd7: begin // 测试DP段
                    red_led <= 8'h00;
                    green_led <= 8'b00001000;
                    yellow_led <= 8'h00;
                end
                4'd8: begin // 全段亮
                    red_led <= 8'hFF;
                    green_led <= 8'hFF;
                    yellow_led <= 8'h00;
                end
                4'd9: begin // 全段灭
                    red_led <= 8'h00;
                    green_led <= 8'h00;
                    yellow_led <= 8'hFF;
                end
                default: begin
                    red_led <= 8'h00;
                    green_led <= 8'h00;
                    yellow_led <= 8'h00;
                end
            endcase
        end
    end

endmodule
