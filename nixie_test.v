`timescale 1ns / 1ps

//////////////////////////////////////////////////////////////////////////////////
// 数码管测试模块 - 简化版本用于调试数码管显示问题
// 功能：显示固定数字"12345678"，验证数码管硬件连接
//////////////////////////////////////////////////////////////////////////////////

module nixie_test(
    input wire clk,                    // 100MHz时钟
    input wire reset,                  // 复位信号
    
    // 数码管输出
    output reg [7:0] nixie_enable,     // 数码管位选 A7-A0 (低电平有效)
    output reg [7:0] nixie_segment     // 数码管段选 CA-CG,DP (低电平有效)
);

    // 时钟分频器
    reg [17:0] counter;                // 分频计数器
    reg [2:0] nixie_counter;           // 数码管扫描计数器
    
    // 固定显示数据 "12345678"
    reg [3:0] digit [7:0];
    
    // 段码表 (共阴极，低电平点亮)
    reg [7:0] segment_code [15:0];
    
    // 初始化段码表和显示数据
    initial begin
        // 数码管段码表 (重新修正位序)
        // 实际位序: nixie_segment[7:0] = {CA,CB,CC,CD,CE,CF,CG,DP}
        // 对应引脚: {F15,F13,F14,F16,E17,C14,C15,E13}
        // 根据您的参考代码，重新排列位序
        segment_code[0]  = 8'b11000000; // 0: CA,CB,CC,CD,CE,CF亮，CG,DP灭
        segment_code[1]  = 8'b11111001; // 1: CB,CC亮
        segment_code[2]  = 8'b10100100; // 2: CA,CB,CG,CE,CD亮
        segment_code[3]  = 8'b10110000; // 3: CA,CB,CG,CC,CD亮
        segment_code[4]  = 8'b10011001; // 4: CF,CG,CB,CC亮
        segment_code[5]  = 8'b10010010; // 5: CA,CF,CG,CC,CD亮
        segment_code[6]  = 8'b10000010; // 6: CA,CF,CG,CE,CD,CC亮
        segment_code[7]  = 8'b11111000; // 7: CA,CB,CC亮
        segment_code[8]  = 8'b00000001; // 8: 全亮(除DP)
        segment_code[9]  = 8'b10010000; // 9: CA,CB,CC,CD,CF,CG亮
        segment_code[10] = 8'b10001000; // A
        segment_code[11] = 8'b10000011; // b
        segment_code[12] = 8'b11000110; // C
        segment_code[13] = 8'b10100001; // d
        segment_code[14] = 8'b10000110; // E
        segment_code[15] = 8'b10001110; // F
        
        // 固定显示 "12345678"
        digit[0] = 4'd8;  // 最右边显示8
        digit[1] = 4'd7;  // 显示7
        digit[2] = 4'd6;  // 显示6
        digit[3] = 4'd5;  // 显示5
        digit[4] = 4'd4;  // 显示4
        digit[5] = 4'd3;  // 显示3
        digit[6] = 4'd2;  // 显示2
        digit[7] = 4'd1;  // 最左边显示1
    end
    
    // 时钟分频和数码管扫描
    always @(posedge clk) begin
        if (reset) begin
            counter <= 18'd0;
            nixie_counter <= 3'd0;
            nixie_enable <= 8'hFF;   // 全部关闭
            nixie_segment <= 8'hFF;  // 全部关闭
        end else begin
            counter <= counter + 1;
            
            // 每2^15 = 32768个时钟周期切换一次 (约0.33ms)
            if (counter[14:0] == 15'd0) begin
                // 选择当前数码管 (低电平有效)
                nixie_enable <= ~(8'b00000001 << nixie_counter);
                
                // 输出对应的段码
                nixie_segment <= segment_code[digit[nixie_counter]];
                
                // 切换到下一个数码管
                nixie_counter <= nixie_counter + 1;
            end
        end
    end

endmodule

//////////////////////////////////////////////////////////////////////////////////
// 测试顶层模块 - 用于替换原始的pill_bottle_system进行测试
//////////////////////////////////////////////////////////////////////////////////

module nixie_test_top(
    input wire clk,                    // 100MHz时钟 Y18
    input wire reset,                  // 复位信号 P20
    
    // 数码管输出
    output wire [7:0] nixie_enable,    // 数码管位选 A7-A0
    output wire [7:0] nixie_segment,   // 数码管段选 CA-CG,DP
    
    // LED输出 (用于指示系统运行)
    output reg [7:0] red_led,          // 红色LED
    output reg [7:0] green_led,        // 绿色LED  
    output reg [7:0] yellow_led        // 黄色LED
);

    // 实例化数码管测试模块
    nixie_test nixie_test_inst(
        .clk(clk),
        .reset(reset),
        .nixie_enable(nixie_enable),
        .nixie_segment(nixie_segment)
    );
    
    // LED指示系统运行状态
    reg [26:0] led_counter;
    
    always @(posedge clk) begin
        if (reset) begin
            led_counter <= 27'd0;
            red_led <= 8'h00;
            green_led <= 8'h00;
            yellow_led <= 8'h00;
        end else begin
            led_counter <= led_counter + 1;
            
            // 1秒闪烁一次，表示系统正常运行
            if (led_counter >= 27'd99_999_999) begin
                led_counter <= 27'd0;
                yellow_led <= ~yellow_led;  // 黄色LED闪烁
            end
            
            // 绿色LED显示流水灯
            green_led <= 8'b00000001 << led_counter[23:21];
            
            // 红色LED全灭表示正常
            red_led <= 8'h00;
        end
    end

endmodule
