# 药片装瓶系统 - Verilog实现

## 项目概述

这是一个基于FPGA的智能药片装瓶控制系统，使用Verilog HDL开发，适用于Xilinx xc7a100tfgg484-1 FPGA平台。

## 功能特性

### 🎯 核心功能
- **参数设置**：支持设置药瓶总数(0-999)和单瓶药片数(0-999)
- **双工作模式**：普通模式(连续装瓶)和定制模式(每瓶后暂停重设)
- **实时显示**：8位数码管显示系统状态和工作进度
- **状态指示**：LED灯显示系统状态和工作进度
- **用户交互**：4×4矩阵键盘输入，按键控制

### 🔧 硬件接口
- **时钟**：100MHz主时钟
- **按键**：6个按键(S1-S6)，其中S6为复位
- **拨码开关**：24个开关，SW0控制模式，SW1控制显示
- **矩阵键盘**：4×4键盘，支持0-9数字输入
- **LED指示**：红色(异常)、绿色(进度)、黄色(状态)各8个
- **数码管**：8位七段数码管显示

## 文件结构

```
pill_2/
├── Top.v                 # 主系统文件(包含所有模块)
├── pill_bottle.xdc       # 约束文件
├── pill_bottle_tb.v      # 测试文件
└── README.md            # 说明文档
```

## 使用说明

### 🚀 操作流程

#### 1. 系统启动
- 上电后系统显示 `00000000`
- 黄色LED显示 `11` (二进制)
- 按S1(开始键)进入设置模式

#### 2. 设置药瓶总数
- 使用矩阵键盘输入数字(1-999)
- 数码管实时显示输入值
- 按S2(确认键)保存设置
- 黄色LED变为 `01`

#### 3. 设置单瓶药片数
- 使用矩阵键盘输入数字(1-999)
- 数码管实时显示输入值
- 按S2(确认键)保存设置
- 黄色LED变为 `00`，系统开始工作

#### 4. 工作过程
- 绿色LED显示流水灯效果(0.5秒移动)
- 数码管显示工作状态：
  - SW1=0：显示配置参数(瓶数+药片数)
  - SW1=1：显示实时状态(完成瓶数+当前瓶药片数)
- 每秒装入一片药片

#### 5. 工作模式
- **普通模式**(SW0=0)：连续装瓶直到完成
- **定制模式**(SW0=1)：每瓶完成后暂停，重新设置下一瓶药片数

### 🎮 控制说明

#### 按键功能
- **S1**：开始/启动系统
- **S2**：确认输入
- **S6**：系统复位

#### 拨码开关
- **SW0**：工作模式选择(0=普通，1=定制)
- **SW1**：显示内容切换(0=配置，1=实时状态)

#### 矩阵键盘布局
```
1  2  3  A
4  5  6  B  
7  8  9  C
*  0  #  D
```
- 数字键0-9：参数输入
- 其他键：保留功能

### 💡 LED指示说明

#### 黄色LED状态指示
- `11`：正在设置药瓶总数
- `01`：正在设置单瓶药片数  
- `00`：工作中或空闲

#### 绿色LED进度指示
- 工作时：流水灯效果显示进度
- 完成时：全部点亮

#### 红色LED异常指示
- 全亮：输入参数错误或系统异常
- 全灭：系统正常

## 开发环境

### 🛠️ 工具要求
- **开发工具**：Xilinx Vivado
- **目标器件**：xc7a100tfgg484-1
- **开发语言**：Verilog HDL

### 📋 项目配置
1. 创建新的Vivado项目
2. 添加源文件：`Top.v`
3. 添加约束文件：`pill_bottle.xdc`
4. 设置顶层模块：`pill_bottle_system`
5. 综合、实现、生成比特流
6. 下载到FPGA

### 🧪 仿真测试
- 测试文件：`pill_bottle_tb.v`
- 运行仿真验证基本功能
- 检查状态转换和时序

## 技术特点

### ⚡ 设计亮点
- **模块化设计**：功能清晰，易于维护
- **精确时序**：基于100MHz时钟的精确控制
- **防抖处理**：20ms键盘防抖，提高可靠性
- **动态显示**：4ms数码管刷新，无闪烁
- **状态机控制**：清晰的状态转换逻辑

### 🔒 可靠性保证
- 参数有效性验证
- 异常检测和处理
- 按键防抖机制
- 系统复位功能

## 故障排除

### 常见问题
1. **数码管不显示**：检查约束文件引脚分配
2. **按键无响应**：确认按键连接和防抖设置
3. **LED不亮**：检查电平逻辑和引脚配置
4. **键盘输入错误**：验证矩阵键盘扫描逻辑

### 调试建议
- 使用ILA(集成逻辑分析仪)观察内部信号
- 检查时钟分频器是否正常工作
- 验证状态机转换逻辑
- 确认约束文件与硬件匹配

## 扩展功能

### 可能的改进
- 添加LCD显示屏支持
- 增加串口通信功能
- 支持更多工作模式
- 添加数据存储功能
- 实现远程监控

## 版权信息

本项目为教学演示用途，基于北京邮电大学数字逻辑课程设计要求开发。

---

**开发平台**：FPGA Minisys实验板  
**开发语言**：Verilog HDL  
**目标器件**：Xilinx xc7a100tfgg484-1
