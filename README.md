# 药片装瓶系统 - Verilog实现

## 项目概述

这是一个基于FPGA的智能药片装瓶控制系统，使用Verilog HDL开发，适用于Xilinx xc7a100tfgg484-1 FPGA平台。

## 功能特性

### 🎯 核心功能
- **参数设置**：支持设置药瓶总数(0-999)和单瓶药片数(0-999)
- **双工作模式**：普通模式(连续装瓶)和定制模式(每瓶后暂停重设)
- **实时显示**：8位数码管显示系统状态和工作进度
- **状态指示**：LED灯显示系统状态和工作进度
- **用户交互**：4×4矩阵键盘输入，按键控制

### 🔧 硬件接口
- **时钟**：100MHz主时钟
- **按键**：6个按键(S1-S6)，其中S6为复位
- **拨码开关**：24个开关，SW0控制模式，SW1控制显示
- **矩阵键盘**：4×4键盘，支持0-9数字输入
- **LED指示**：红色(异常)、绿色(进度)、黄色(状态)各8个
- **数码管**：8位七段数码管显示

## 文件结构

```
pill_2/
├── Top.v                 # 主系统文件(包含所有模块)
├── pill_bottle.xdc       # 约束文件
├── pill_bottle_tb.v      # 测试文件
└── README.md            # 说明文档
```

## 使用说明

### 🚀 操作流程

#### 1. 系统启动
- 上电后系统显示 `00000000`
- 黄色LED显示 `11` (二进制)
- 按S1(开始键)进入设置模式

#### 2. 设置药瓶总数
- 使用矩阵键盘输入数字(1-999)
- 数码管实时显示输入值
- 按S2(确认键)保存设置
- 黄色LED变为 `01`

#### 3. 设置单瓶药片数
- 使用矩阵键盘输入数字(1-999)
- 数码管实时显示输入值
- 按S2(确认键)保存设置
- 黄色LED变为 `00`，系统开始工作

#### 4. 工作过程
- 绿色LED显示流水灯效果(0.5秒移动)
- 数码管显示工作状态：
  - SW1=0：显示配置参数(瓶数+药片数)
  - SW1=1：显示实时状态(完成瓶数+当前瓶药片数)
- 每秒装入一片药片

#### 5. 工作模式
- **普通模式**(SW0=0)：连续装瓶直到完成
- **定制模式**(SW0=1)：每瓶完成后暂停，重新设置下一瓶药片数

### 🎮 控制说明

#### 按键功能
- **S1**：开始/启动系统
- **S2**：确认输入
- **S6**：系统复位

#### 拨码开关
- **SW0**：工作模式选择(0=普通，1=定制)
- **SW1**：显示内容切换(0=配置，1=实时状态)

#### 矩阵键盘布局
```
1  2  3  A
4  5  6  B  
7  8  9  C
*  0  #  D
```
- 数字键0-9：参数输入
- 其他键：保留功能

### 💡 LED指示说明

#### 黄色LED状态指示
- `11`：正在设置药瓶总数
- `01`：正在设置单瓶药片数  
- `00`：工作中或空闲

#### 绿色LED进度指示
- 工作时：流水灯效果显示进度
- 完成时：全部点亮

#### 红色LED异常指示
- 全亮：输入参数错误或系统异常
- 全灭：系统正常

## 开发环境

### 🛠️ 工具要求
- **开发工具**：Xilinx Vivado
- **目标器件**：xc7a100tfgg484-1
- **开发语言**：Verilog HDL

### 📋 项目配置

#### 方法1：使用TCL脚本（推荐）
1. 在Vivado TCL控制台中运行：`source create_project.tcl`
2. 项目将自动创建并配置完成

#### 方法2：手动创建
1. 创建新的Vivado项目
2. 选择器件：`xc7a100tfgg484-1`
3. 添加源文件：`Top.v`
4. 添加约束文件：`pill_bottle.xdc`
5. 设置顶层模块：`pill_bottle_system`
6. 综合、实现、生成比特流
7. 下载到FPGA

#### 重要说明
- 约束文件已修复所有DRC错误
- 所有端口都已正确分配引脚
- 支持完整的功能验证

### 🧪 仿真测试
- 测试文件：`pill_bottle_tb.v`
- 运行仿真验证基本功能
- 检查状态转换和时序

## 技术特点

### ⚡ 设计亮点
- **模块化设计**：功能清晰，易于维护
- **精确时序**：基于100MHz时钟的精确控制
- **防抖处理**：20ms键盘防抖，提高可靠性
- **动态显示**：4ms数码管刷新，无闪烁
- **状态机控制**：清晰的状态转换逻辑

### 🔒 可靠性保证
- 参数有效性验证
- 异常检测和处理
- 按键防抖机制
- 系统复位功能

## 故障排除

### 常见问题及解决方案

#### 1. DRC错误：UCIO-1 Unconstrained Logical Port
**问题**：端口没有分配具体的引脚位置
**常见原因**：
- 约束文件未正确添加到项目
- 端口名称不匹配
- 约束语法错误

**解决步骤**：
1. **验证约束文件**：
   ```tcl
   source verify_constraints.tcl
   ```

2. **检查项目设置**：
   - 确保`pill_bottle.xdc`已添加到约束文件集
   - 顶层模块设置为`pill_bottle_system`
   - 重新运行综合

3. **手动检查端口**：
   ```tcl
   source check_ports.tcl
   ```

4. **如果问题持续**：
   - 检查Verilog端口声明与约束文件是否完全匹配
   - 确认约束文件语法正确（无花括号问题）

#### 2. 银行电压冲突错误（BIVC-1）
**问题**：同一IO银行中混用不同电压标准
**错误示例**：
```
Bank IO standard Vcc: Conflicting Vcc voltages in bank 35
confirm_btn (LVCMOS15) and keypad_col[0] (LVCMOS33)
```
**解决方案**：
- 统一同一银行内所有信号的电压标准（推荐使用LVCMOS33）
- 或重新分配引脚到不同银行
- 当前约束文件已修复此问题

#### 3. 引脚冲突错误
**问题**：多个信号分配到同一引脚
**解决方案**：
- 检查约束文件中是否有重复的`PACKAGE_PIN`分配
- 确认硬件连接与约束文件一致
- 使用不同的未使用引脚

#### 4. 数码管显示问题
**问题**：数码管不显示或显示错误

**常见原因**：
- 硬件连接问题（电源、引脚）
- 段码表定义错误
- 扫描频率问题
- 时钟分频逻辑错误

**调试步骤**：
1. **使用测试模块**：
   ```tcl
   source create_nixie_test.tcl
   ```

2. **检查硬件连接**：
   - 确认数码管电源连接
   - 验证引脚连接正确
   - 确认数码管类型（共阴极）

3. **观察测试结果**：
   - 应显示"********"
   - LED指示系统运行状态

4. **如果测试失败**：
   - 检查约束文件引脚分配
   - 验证电平标准设置
   - 使用示波器检查信号

**详细调试指南**：参见`nixie_debug_guide.md`

#### 5. 数码管显示数字错误
**问题**：数码管能显示但数字不正确

**原因**：段码表与实际硬件不匹配

**解决方案**：
1. **使用修正的段码表**：
   ```verilog
   segment_code[0] = 8'b00111111; // 0 - 正确
   segment_code[1] = 8'b00000110; // 1 - 正确
   segment_code[2] = 8'b01011011; // 2 - 正确
   ```

2. **验证显示**：
   - 测试模块应显示"********"
   - 主系统初始显示"00000000"

3. **段码表参考**：详见`segment_code_reference.md`

#### 6. 按键无响应
**问题**：按键按下没有反应
**解决方案**：
- 检查按键引脚的电平标准（已统一为LVCMOS33）
- 验证防抖逻辑设置（20ms）
- 确认边沿检测逻辑正确
- 如果硬件确实是1.5V，可能需要电平转换

#### 7. 矩阵键盘输入错误
**问题**：键盘输入值不正确
**解决方案**：
- 验证行列扫描逻辑
- 检查键值映射表
- 确认键盘连接正确

### 调试建议
- 使用ILA(集成逻辑分析仪)观察内部信号
- 检查时钟分频器是否正常工作
- 验证状态机转换逻辑
- 确认约束文件与硬件匹配
- 使用仿真验证逻辑功能

## 扩展功能

### 可能的改进
- 添加LCD显示屏支持
- 增加串口通信功能
- 支持更多工作模式
- 添加数据存储功能
- 实现远程监控

## 版权信息

本项目为教学演示用途，基于北京邮电大学数字逻辑课程设计要求开发。

---

**开发平台**：FPGA Minisys实验板  
**开发语言**：Verilog HDL  
**目标器件**：Xilinx xc7a100tfgg484-1
