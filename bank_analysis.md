# FPGA IO银行分析和电压冲突解决方案

## 问题描述

在Xilinx FPGA中，IO引脚被分组到不同的银行（Bank）中。每个银行必须使用相同的VCCO电压。如果在同一个银行中混用不同电压标准的IO标准，会导致DRC错误。

## 错误信息分析

```
[DRC BIVC-1] Bank IO standard Vcc: Conflicting Vcc voltages in bank 35. 
For example, the following two ports in this bank have conflicting VCCOs:  
confirm_btn (LVCMOS15, requiring VCCO=1.500) and keypad_col[0] (LVCMOS33, requiring VCCO=3.300)
```

这表明：
- Bank 35中有引脚冲突
- `confirm_btn`使用LVCMOS15（需要1.5V）
- `keypad_col[0]`使用LVCMOS33（需要3.3V）
- 同一银行不能同时支持两种电压

## 解决方案

### 方案1：统一电压标准（已采用）

将所有相关引脚统一使用LVCMOS33：

```tcl
# 修改前（有冲突）
set_property IOSTANDARD LVCMOS15 [get_ports confirm_btn]
set_property IOSTANDARD LVCMOS33 [get_ports {keypad_col[0]}]

# 修改后（无冲突）
set_property IOSTANDARD LVCMOS33 [get_ports confirm_btn]
set_property IOSTANDARD LVCMOS33 [get_ports {keypad_col[0]}]
```

### 方案2：重新分配引脚

如果硬件确实需要不同电压，可以：
1. 查看FPGA引脚图，找到不同银行的引脚
2. 将需要1.5V的信号分配到支持1.5V的银行
3. 将需要3.3V的信号分配到支持3.3V的银行

## xc7a100tfgg484-1 银行信息

### 主要IO银行分布
- **Bank 14**: 高性能IO，支持多种电压
- **Bank 15**: 高性能IO，支持多种电压  
- **Bank 16**: 高性能IO，支持多种电压
- **Bank 34**: 高密度IO，通常3.3V
- **Bank 35**: 高密度IO，通常3.3V

### 电压支持
- **LVCMOS33**: 3.3V，大多数银行支持
- **LVCMOS25**: 2.5V，部分银行支持
- **LVCMOS18**: 1.8V，部分银行支持
- **LVCMOS15**: 1.5V，特定银行支持

## 硬件考虑

### 实际硬件电压
根据您提供的引脚信息：
- S1-S5按键标注为1.5V
- S6按键标注为3.3V
- 其他信号大多为3.3V

### 兼容性处理
1. **如果硬件确实是1.5V**：
   - 需要确保FPGA银行配置支持1.5V
   - 可能需要外部电平转换电路

2. **如果可以容忍3.3V**：
   - 大多数1.5V逻辑可以容忍3.3V输入
   - 需要检查具体器件规格

## 推荐做法

### 1. 验证硬件规格
```bash
# 检查实际硬件电压要求
# 查看开发板原理图
# 确认按键电路设计
```

### 2. 测试兼容性
```verilog
// 在实际硬件上测试
// 验证3.3V设置是否正常工作
// 检查信号完整性
```

### 3. 文档记录
```markdown
# 记录最终选择的电压标准
# 说明兼容性考虑
# 提供替代方案
```

## 当前解决方案

在`pill_bottle.xdc`中，我们采用了**方案1**：
- 所有按键信号统一使用LVCMOS33
- 避免银行电压冲突
- 简化设计复杂度

### 优点
- ✅ 解决DRC错误
- ✅ 简化约束文件
- ✅ 提高兼容性

### 注意事项
- ⚠️ 需要验证硬件兼容性
- ⚠️ 可能需要调整输入阈值
- ⚠️ 建议在实际硬件上测试

## 验证步骤

1. **综合检查**：确认无DRC错误
2. **时序分析**：验证信号完整性
3. **硬件测试**：在实际板卡上验证功能
4. **长期稳定性**：确保可靠运行

## 备用方案

如果3.3V设置在实际硬件上不工作：

1. **查找银行映射**：
   ```tcl
   # 在Vivado中查看引脚银行信息
   get_ports -filter {DIRECTION == IN}
   report_property [get_ports confirm_btn] PACKAGE_PIN
   ```

2. **重新分配引脚**：
   - 将1.5V信号移到支持1.5V的银行
   - 更新约束文件

3. **添加电平转换**：
   - 硬件上添加电平转换芯片
   - 保持原始电压设计
