# 药片装瓶系统约束文件
# 开发平台：FPGA Minisys实验板 (Xilinx xc7a100tfgg484-1)

#==============================================================================
# 时钟约束
#==============================================================================
# 100MHz主时钟
set_property PACKAGE_PIN Y18 [get_ports clk]
set_property IOSTANDARD LVCMOS33 [get_ports clk]
create_clock -add -name sys_clk_pin -period 10.00 -waveform {0 5} [get_ports clk]

#==============================================================================
# 复位和按键约束
#==============================================================================
# 复位按键 S6 (3.3V)
set_property PACKAGE_PIN P20 [get_ports reset]
set_property IOSTANDARD LVCMOS33 [get_ports reset]

# 按键信号 - 统一使用3.3V电平标准避免银行冲突
# 注意：根据实际硬件情况，如果按键确实是1.5V，需要检查银行分配
set_property PACKAGE_PIN R1 [get_ports start_btn]
set_property IOSTANDARD LVCMOS33 [get_ports start_btn]

set_property PACKAGE_PIN P1 [get_ports confirm_btn]
set_property IOSTANDARD LVCMOS33 [get_ports confirm_btn]

# 其他按键 - 统一使用3.3V电平标准
set_property PACKAGE_PIN P5 [get_ports {other_btns[0]}]  # S3
set_property PACKAGE_PIN P4 [get_ports {other_btns[1]}]  # S4
set_property PACKAGE_PIN P2 [get_ports {other_btns[2]}]  # S5
set_property PACKAGE_PIN T4 [get_ports {other_btns[3]}]  # 占位引脚
set_property IOSTANDARD LVCMOS33 [get_ports {other_btns[*]}]

#==============================================================================
# 拨码开关约束
#==============================================================================
# 模式切换开关 SW0
set_property PACKAGE_PIN W4 [get_ports mode_switch]
set_property IOSTANDARD LVCMOS33 [get_ports mode_switch]

# 显示切换开关 SW1
set_property PACKAGE_PIN R4 [get_ports display_switch]
set_property IOSTANDARD LVCMOS33 [get_ports display_switch]

# 其他拨码开关 SW3-SW23 (SW2=T4已被other_btns[3]使用)
set_property PACKAGE_PIN T5 [get_ports {other_switches[0]}]   # SW3
set_property PACKAGE_PIN U5 [get_ports {other_switches[1]}]   # SW4
set_property PACKAGE_PIN W6 [get_ports {other_switches[2]}]   # SW5
set_property PACKAGE_PIN W5 [get_ports {other_switches[3]}]   # SW6
set_property PACKAGE_PIN U6 [get_ports {other_switches[4]}]   # SW7
set_property PACKAGE_PIN V5 [get_ports {other_switches[5]}]   # SW8
set_property PACKAGE_PIN R6 [get_ports {other_switches[6]}]   # SW9
set_property PACKAGE_PIN T6 [get_ports {other_switches[7]}]   # SW10
set_property PACKAGE_PIN Y6 [get_ports {other_switches[8]}]   # SW11
set_property PACKAGE_PIN AA6 [get_ports {other_switches[9]}]  # SW12
set_property PACKAGE_PIN V7 [get_ports {other_switches[10]}]  # SW13
set_property PACKAGE_PIN AB7 [get_ports {other_switches[11]}] # SW14
set_property PACKAGE_PIN AB6 [get_ports {other_switches[12]}] # SW15
set_property PACKAGE_PIN V9 [get_ports {other_switches[13]}]  # SW16
set_property PACKAGE_PIN V8 [get_ports {other_switches[14]}]  # SW17
set_property PACKAGE_PIN AA8 [get_ports {other_switches[15]}] # SW18
set_property PACKAGE_PIN AB8 [get_ports {other_switches[16]}] # SW19
set_property PACKAGE_PIN Y8 [get_ports {other_switches[17]}]  # SW20
set_property PACKAGE_PIN Y7 [get_ports {other_switches[18]}]  # SW21
set_property PACKAGE_PIN W9 [get_ports {other_switches[19]}]  # SW22
set_property PACKAGE_PIN Y9 [get_ports {other_switches[20]}]  # SW23
# 填充剩余的other_switches端口
set_property PACKAGE_PIN L4 [get_ports {other_switches[21]}]  # 使用未分配引脚
set_property IOSTANDARD LVCMOS33 [get_ports {other_switches[*]}]

#==============================================================================
# 4×4矩阵键盘约束
#==============================================================================
# 键盘列输入 K1-K4
set_property PACKAGE_PIN K4 [get_ports {keypad_col[0]}]
set_property PACKAGE_PIN J4 [get_ports {keypad_col[1]}]
set_property PACKAGE_PIN L3 [get_ports {keypad_col[2]}]
set_property PACKAGE_PIN K3 [get_ports {keypad_col[3]}]
set_property IOSTANDARD LVCMOS33 [get_ports {keypad_col[*]}]

# 键盘行输出 K5,K6,K7,K8,K9 (只用前4个)
set_property PACKAGE_PIN M2 [get_ports {keypad_row[0]}]
set_property PACKAGE_PIN K6 [get_ports {keypad_row[1]}]
set_property PACKAGE_PIN J6 [get_ports {keypad_row[2]}]
set_property PACKAGE_PIN J5 [get_ports {keypad_row[3]}]
set_property IOSTANDARD LVCMOS33 [get_ports {keypad_row[*]}]

#==============================================================================
# LED约束
#==============================================================================
# 红色LED RLD0-RLD7 (高电平点亮)
set_property PACKAGE_PIN N19 [get_ports {red_led[0]}]
set_property PACKAGE_PIN N20 [get_ports {red_led[1]}]
set_property PACKAGE_PIN M20 [get_ports {red_led[2]}]
set_property PACKAGE_PIN K13 [get_ports {red_led[3]}]
set_property PACKAGE_PIN K14 [get_ports {red_led[4]}]
set_property PACKAGE_PIN M13 [get_ports {red_led[5]}]
set_property PACKAGE_PIN L13 [get_ports {red_led[6]}]
set_property PACKAGE_PIN K17 [get_ports {red_led[7]}]
set_property IOSTANDARD LVCMOS33 [get_ports {red_led[*]}]

# 绿色LED GLD0-GLD7 (高电平点亮)
set_property PACKAGE_PIN J17 [get_ports {green_led[0]}]
set_property PACKAGE_PIN L14 [get_ports {green_led[1]}]
set_property PACKAGE_PIN L15 [get_ports {green_led[2]}]
set_property PACKAGE_PIN L16 [get_ports {green_led[3]}]
set_property PACKAGE_PIN K16 [get_ports {green_led[4]}]
set_property PACKAGE_PIN M15 [get_ports {green_led[5]}]
set_property PACKAGE_PIN M16 [get_ports {green_led[6]}]
set_property PACKAGE_PIN M17 [get_ports {green_led[7]}]
set_property IOSTANDARD LVCMOS33 [get_ports {green_led[*]}]

# 黄色LED YLD0-YLD7 (高电平点亮)
set_property PACKAGE_PIN A21 [get_ports {yellow_led[0]}]
set_property PACKAGE_PIN E22 [get_ports {yellow_led[1]}]
set_property PACKAGE_PIN D22 [get_ports {yellow_led[2]}]
set_property PACKAGE_PIN E21 [get_ports {yellow_led[3]}]
set_property PACKAGE_PIN D21 [get_ports {yellow_led[4]}]
set_property PACKAGE_PIN G21 [get_ports {yellow_led[5]}]
set_property PACKAGE_PIN G22 [get_ports {yellow_led[6]}]
set_property PACKAGE_PIN F21 [get_ports {yellow_led[7]}]
set_property IOSTANDARD LVCMOS33 [get_ports {yellow_led[*]}]

#==============================================================================
# 数码管约束
#==============================================================================
# 数码管位选 A7-A0 (低电平有效)
set_property PACKAGE_PIN A18 [get_ports nixie_enable[7]]
set_property PACKAGE_PIN A20 [get_ports nixie_enable[6]]
set_property PACKAGE_PIN B20 [get_ports nixie_enable[5]]
set_property PACKAGE_PIN E18 [get_ports nixie_enable[4]]
set_property PACKAGE_PIN F18 [get_ports nixie_enable[3]]
set_property PACKAGE_PIN D19 [get_ports nixie_enable[2]]
set_property PACKAGE_PIN E19 [get_ports nixie_enable[1]]
set_property PACKAGE_PIN C19 [get_ports nixie_enable[0]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_enable[7]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_enable[6]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_enable[5]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_enable[4]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_enable[3]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_enable[2]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_enable[1]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_enable[0]]

# 数码管段选 CA-CG,DP (低电平有效)
# nixie_segment[7:0] 对应 {CA,CB,CC,CD,CE,CF,CG,DP}
set_property PACKAGE_PIN F15 [get_ports nixie_segment[7]]
set_property PACKAGE_PIN F13 [get_ports nixie_segment[6]]
set_property PACKAGE_PIN F14 [get_ports nixie_segment[5]]
set_property PACKAGE_PIN F16 [get_ports nixie_segment[4]]
set_property PACKAGE_PIN E17 [get_ports nixie_segment[3]]
set_property PACKAGE_PIN C14 [get_ports nixie_segment[2]]
set_property PACKAGE_PIN C15 [get_ports nixie_segment[1]]
set_property PACKAGE_PIN E13 [get_ports nixie_segment[0]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_segment[7]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_segment[6]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_segment[5]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_segment[4]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_segment[3]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_segment[2]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_segment[1]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_segment[0]]

#==============================================================================
# 时序约束
#==============================================================================
# 设置输入延迟
set_input_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports {keypad_col[*]}]
set_input_delay -clock [get_clocks sys_clk_pin] -max -add_delay 4.000 [get_ports {keypad_col[*]}]

# 设置输出延迟
set_output_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports {nixie_*}]
set_output_delay -clock [get_clocks sys_clk_pin] -max -add_delay 4.000 [get_ports {nixie_*}]

# 设置虚假路径（异步信号）
set_false_path -from [get_ports reset]
set_false_path -from [get_ports start_btn]
set_false_path -from [get_ports confirm_btn]
set_false_path -from [get_ports mode_switch]
set_false_path -from [get_ports display_switch]
