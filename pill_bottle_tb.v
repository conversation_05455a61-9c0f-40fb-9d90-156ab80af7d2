`timescale 1ns / 1ps

//////////////////////////////////////////////////////////////////////////////////
// 药片装瓶系统测试文件
//////////////////////////////////////////////////////////////////////////////////

module pill_bottle_tb();

    // 测试信号
    reg clk;
    reg reset;
    reg start_btn;
    reg confirm_btn;
    reg [3:0] other_btns;
    reg mode_switch;
    reg display_switch;
    reg [21:0] other_switches;
    reg [3:0] keypad_col;
    
    wire [3:0] keypad_row;
    wire [7:0] red_led;
    wire [7:0] green_led;
    wire [7:0] yellow_led;
    wire [7:0] nixie_enable;
    wire [7:0] nixie_segment;
    
    // 实例化被测试模块
    pill_bottle_system uut (
        .clk(clk),
        .reset(reset),
        .start_btn(start_btn),
        .confirm_btn(confirm_btn),
        .other_btns(other_btns),
        .mode_switch(mode_switch),
        .display_switch(display_switch),
        .other_switches(other_switches),
        .keypad_col(keypad_col),
        .keypad_row(keypad_row),
        .red_led(red_led),
        .green_led(green_led),
        .yellow_led(yellow_led),
        .nixie_enable(nixie_enable),
        .nixie_segment(nixie_segment)
    );
    
    // 时钟生成 (100MHz)
    initial begin
        clk = 0;
        forever #5 clk = ~clk;  // 10ns周期 = 100MHz
    end
    
    // 测试序列
    initial begin
        // 初始化信号
        reset = 1;
        start_btn = 0;
        confirm_btn = 0;
        other_btns = 4'b0000;
        mode_switch = 0;  // 普通模式
        display_switch = 0;
        other_switches = 22'b0;
        keypad_col = 4'hF;  // 无按键
        
        // 复位
        #100;
        reset = 0;
        #100;
        
        $display("=== 药片装瓶系统测试开始 ===");
        
        // 测试1：启动系统
        $display("测试1：启动系统");
        start_btn = 1;
        #20;
        start_btn = 0;
        #100;
        
        // 检查状态
        if (yellow_led == 8'b00000011) begin
            $display("✓ 系统正确进入设置药瓶总数状态");
        end else begin
            $display("✗ 系统状态错误");
        end
        
        // 测试2：输入药瓶总数 (模拟按键5)
        $display("测试2：输入药瓶总数 5");
        simulate_keypress(4'h5);
        #1000;
        
        // 确认输入
        confirm_btn = 1;
        #20;
        confirm_btn = 0;
        #100;
        
        // 检查状态
        if (yellow_led == 8'b00000001) begin
            $display("✓ 系统正确进入设置单瓶药片数状态");
        end else begin
            $display("✗ 系统状态错误");
        end
        
        // 测试3：输入单瓶药片数 (模拟按键3)
        $display("测试3：输入单瓶药片数 3");
        simulate_keypress(4'h3);
        #1000;
        
        // 确认输入
        confirm_btn = 1;
        #20;
        confirm_btn = 0;
        #100;
        
        // 检查状态
        if (yellow_led == 8'b00000000) begin
            $display("✓ 系统正确进入工作状态");
        end else begin
            $display("✗ 系统状态错误");
        end
        
        // 测试4：观察工作过程
        $display("测试4：观察工作过程 (等待几秒)");
        #5000000;  // 等待5ms (在仿真中代表较长时间)
        
        $display("=== 测试完成 ===");
        $display("红色LED: %b", red_led);
        $display("绿色LED: %b", green_led);
        $display("黄色LED: %b", yellow_led);
        
        $finish;
    end
    
    // 模拟按键按下的任务
    task simulate_keypress;
        input [3:0] key_val;
        begin
            case (key_val)
                4'h0: begin keypad_col = 4'b1101; end  // 0键
                4'h1: begin keypad_col = 4'b1110; end  // 1键
                4'h2: begin keypad_col = 4'b1101; end  // 2键
                4'h3: begin keypad_col = 4'b1011; end  // 3键
                4'h4: begin keypad_col = 4'b1110; end  // 4键
                4'h5: begin keypad_col = 4'b1101; end  // 5键
                4'h6: begin keypad_col = 4'b1011; end  // 6键
                4'h7: begin keypad_col = 4'b1110; end  // 7键
                4'h8: begin keypad_col = 4'b1101; end  // 8键
                4'h9: begin keypad_col = 4'b1011; end  // 9键
                default: keypad_col = 4'hF;
            endcase
            
            #50000;  // 保持按下50us
            keypad_col = 4'hF;  // 释放按键
            #50000;  // 等待50us
        end
    endtask
    
    // 监控重要信号
    always @(posedge clk) begin
        if (uut.current_state != uut.current_state) begin
            case (uut.current_state)
                2'b00: $display("状态变化：IDLE");
                2'b01: $display("状态变化：SET_BOTTLE_NUM");
                2'b10: $display("状态变化：SET_PILL_NUM");
                2'b11: $display("状态变化：WORKING");
            endcase
        end
    end

endmodule
