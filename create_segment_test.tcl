# 段测试项目创建脚本
# 用于逐个测试数码管的每个段，确定正确的段码表

set project_name "segment_test"
set project_dir "./segment_test_project"
set part_name "xc7a100tfgg484-1"

puts "=== 创建数码管段测试项目 ==="

# 删除旧项目
if {[file exists $project_dir]} {
    file delete -force $project_dir
}

# 创建项目
create_project $project_name $project_dir -part $part_name -force

# 添加源文件
add_files -norecurse {segment_test.v}

# 添加约束文件
add_files -fileset constrs_1 -norecurse {segment_test.xdc}

# 设置顶层模块
set_property top segment_test_top [current_fileset]

# 更新编译顺序
update_compile_order -fileset sources_1

puts "项目创建完成！"

# 运行综合
puts "\n正在运行综合..."
if {[catch {synth_design -top segment_test_top} result]} {
    puts "综合失败: $result"
    return
} else {
    puts "综合成功！"
}

# 检查端口约束
set unconstrained_ports [get_ports -filter {LOC == ""}]
if {[llength $unconstrained_ports] > 0} {
    puts "⚠️  发现未约束端口:"
    foreach port $unconstrained_ports {
        puts "  - $port"
    }
    return
} else {
    puts "✓ 所有端口都已正确约束"
}

# 运行实现
puts "\n正在运行实现..."
if {[catch {
    opt_design
    place_design
    route_design
} impl_result]} {
    puts "实现失败: $impl_result"
    return
} else {
    puts "实现成功！"
}

# 生成比特流
puts "\n正在生成比特流..."
if {[catch {write_bitstream -force segment_test.bit} bit_result]} {
    puts "比特流生成失败: $bit_result"
} else {
    puts "比特流生成成功: segment_test.bit"
}

puts "\n=== 段测试项目完成 ==="
puts "\n测试说明："
puts "下载比特流后，观察数码管第一位的显示变化："
puts "- 每2秒切换一次测试状态"
puts "- LED指示当前测试的段："
puts "  红色LED[0-3]: 测试CA,CB,CC,CD段"
puts "  绿色LED[0-3]: 测试CE,CF,CG,DP段"
puts "  红绿全亮: 全段测试"
puts "  黄色全亮: 全段关闭"
puts "\n观察哪个LED亮时，数码管显示对应的段"
puts "记录下每个段对应的位置，用于修正段码表"
