`timescale 1ns / 1ps

//////////////////////////////////////////////////////////////////////////////////
// 药片装瓶系统 - 完整实现
// 功能：智能药片装瓶控制系统，支持参数设置、两种工作模式、实时显示
// 开发平台：FPGA Minisys实验板 (Xilinx xc7a100tfgg484-1)
//////////////////////////////////////////////////////////////////////////////////

module pill_bottle_system(
    input wire clk,                    // 100MHz时钟
    input wire reset,                  // 复位信号 S6

    // 按键输入
    input wire start_btn,              // S1: 开始按键
    input wire confirm_btn,            // S2: 确认按键
    input wire [3:0] other_btns,       // S3-S5: 其他按键

    // 拨码开关
    input wire mode_switch,            // SW0: 模式切换 (0:普通模式, 1:定制模式)
    input wire display_switch,         // SW1: 显示切换
    input wire [21:0] other_switches,  // SW2-SW23: 其他开关

    // 4×4矩阵键盘
    input wire [3:0] keypad_col,       // 键盘列输入 K1,K2,K3,K4
    output reg [3:0] keypad_row,       // 键盘行输出 K5,K6,K7,K8,K9

    // LED输出
    output reg [7:0] red_led,          // 红色LED (异常指示)
    output reg [7:0] green_led,        // 绿色LED (工作进度)
    output reg [7:0] yellow_led,       // 黄色LED (状态指示)

    // 数码管输出
    output reg [7:0] nixie_enable,     // 数码管位选 A7-A0 (低电平有效)
    output reg [7:0] nixie_segment     // 数码管段选 CA-CG,DP (低电平有效)
);

    // 系统状态定义
    parameter IDLE = 2'b00;            // 空闲状态
    parameter SET_BOTTLE_NUM = 2'b01;  // 设置药瓶总数
    parameter SET_PILL_NUM = 2'b10;    // 设置单瓶药片数
    parameter WORKING = 2'b11;         // 工作状态

    // 内部寄存器
    reg [1:0] current_state;           // 当前状态
    reg [9:0] max_bot_num;             // 药瓶总数 (0-999)
    reg [9:0] max_sgl_bot;             // 单瓶药片数 (0-999)
    reg [9:0] now_bot_bil_num;         // 当前瓶内药片计数
    reg [9:0] bot_finished;            // 已完成瓶数
    reg [9:0] input_buffer;            // 输入缓冲区
    reg [31:0] display_data;           // 显示数据
    reg error_flag;                    // 错误标志

    // 时钟分频器
    reg [26:0] second_counter;         // 1秒计数器
    reg [19:0] ms_counter;             // 毫秒计数器
    reg [2:0] nixie_counter;           // 数码管扫描计数器
    reg [1:0] key_scan_counter;        // 键盘扫描计数器
    reg second_pulse;                  // 1秒脉冲
    reg ms_4_pulse;                    // 4ms脉冲
    reg ms_20_pulse;                   // 20ms脉冲

    // 按键处理
    reg start_btn_sync, start_btn_prev;
    reg confirm_btn_sync, confirm_btn_prev;
    wire start_btn_edge, confirm_btn_edge;

    // 键盘相关
    reg [3:0] key_value;               // 键盘输入值
    reg key_valid;                     // 键盘输入有效
    reg [3:0] key_debounce_counter;    // 键盘防抖计数器
    reg [3:0] last_key_col;            // 上次键盘列状态

    // 数码管显示相关
    reg [3:0] digit [7:0];             // 8位数字存储
    reg [7:0] segment_code [15:0];     // 段码表 (0-F)

    // LED控制相关
    reg [26:0] led_counter;            // LED流水灯计数器
    reg [2:0] led_position;            // LED位置

    //==========================================================================
    // 初始化段码表
    //==========================================================================
    initial begin
        // 数码管段码表 (根据实际硬件修正)
        // 格式: {DP,CG,CF,CE,CD,CC,CB,CA}
        segment_code[0]  = 8'b00111111; // 0
        segment_code[1]  = 8'b00000110; // 1
        segment_code[2]  = 8'b01011011; // 2
        segment_code[3]  = 8'b01001111; // 3
        segment_code[4]  = 8'b01100110; // 4
        segment_code[5]  = 8'b01101101; // 5
        segment_code[6]  = 8'b01111101; // 6
        segment_code[7]  = 8'b00000111; // 7
        segment_code[8]  = 8'b01111111; // 8
        segment_code[9]  = 8'b01101111; // 9
        segment_code[10] = 8'b01110111; // A
        segment_code[11] = 8'b01111100; // b
        segment_code[12] = 8'b00111001; // C
        segment_code[13] = 8'b01011110; // d
        segment_code[14] = 8'b01111001; // E
        segment_code[15] = 8'b01110001; // F
    end

    //==========================================================================
    // 时钟分频器
    //==========================================================================
    always @(posedge clk) begin
        if (reset) begin
            second_counter <= 27'd0;
            ms_counter <= 20'd0;
            second_pulse <= 1'b0;
            ms_4_pulse <= 1'b0;
            ms_20_pulse <= 1'b0;
        end else begin
            // 1秒计数器 (100MHz -> 1Hz)
            if (second_counter >= 27'd99_999_999) begin
                second_counter <= 27'd0;
                second_pulse <= 1'b1;
            end else begin
                second_counter <= second_counter + 1;
                second_pulse <= 1'b0;
            end

            // 毫秒计数器 (修正计数逻辑)
            ms_counter <= ms_counter + 1;

            // 4ms脉冲生成 (每400,000个时钟周期 = 4ms)
            if (ms_counter >= 20'd399_999) begin
                ms_4_pulse <= 1'b1;
                if (ms_counter >= 20'd1_999_999) begin  // 20ms
                    ms_20_pulse <= 1'b1;
                    ms_counter <= 20'd0;
                end else begin
                    ms_20_pulse <= 1'b0;
                end
            end else begin
                ms_4_pulse <= 1'b0;
                ms_20_pulse <= 1'b0;
            end
        end
    end

    //==========================================================================
    // 按键边沿检测
    //==========================================================================
    always @(posedge clk) begin
        if (reset) begin
            start_btn_sync <= 1'b0;
            start_btn_prev <= 1'b0;
            confirm_btn_sync <= 1'b0;
            confirm_btn_prev <= 1'b0;
        end else begin
            start_btn_sync <= start_btn;
            start_btn_prev <= start_btn_sync;
            confirm_btn_sync <= confirm_btn;
            confirm_btn_prev <= confirm_btn_sync;
        end
    end

    assign start_btn_edge = start_btn_sync & ~start_btn_prev;
    assign confirm_btn_edge = confirm_btn_sync & ~confirm_btn_prev;

    //==========================================================================
    // 4×4矩阵键盘扫描
    //==========================================================================
    always @(posedge clk) begin
        if (reset) begin
            keypad_row <= 4'b1110;  // 初始扫描第一行
            key_scan_counter <= 2'b00;
            key_value <= 4'h0;
            key_valid <= 1'b0;
            key_debounce_counter <= 4'h0;
            last_key_col <= 4'hF;
        end else if (ms_20_pulse) begin  // 20ms扫描一次
            // 行扫描
            case (key_scan_counter)
                2'b00: keypad_row <= 4'b1110;  // 扫描第1行
                2'b01: keypad_row <= 4'b1101;  // 扫描第2行
                2'b10: keypad_row <= 4'b1011;  // 扫描第3行
                2'b11: keypad_row <= 4'b0111;  // 扫描第4行
            endcase

            // 检测按键
            if (keypad_col != 4'hF) begin  // 有按键按下
                if (keypad_col == last_key_col) begin
                    if (key_debounce_counter < 4'hF) begin
                        key_debounce_counter <= key_debounce_counter + 1;
                    end else begin
                        // 防抖完成，确定按键值
                        key_valid <= 1'b1;
                        case ({key_scan_counter, keypad_col})
                            {2'b00, 4'b1110}: key_value <= 4'h1;  // 1
                            {2'b00, 4'b1101}: key_value <= 4'h2;  // 2
                            {2'b00, 4'b1011}: key_value <= 4'h3;  // 3
                            {2'b00, 4'b0111}: key_value <= 4'hA;  // A
                            {2'b01, 4'b1110}: key_value <= 4'h4;  // 4
                            {2'b01, 4'b1101}: key_value <= 4'h5;  // 5
                            {2'b01, 4'b1011}: key_value <= 4'h6;  // 6
                            {2'b01, 4'b0111}: key_value <= 4'hB;  // B
                            {2'b10, 4'b1110}: key_value <= 4'h7;  // 7
                            {2'b10, 4'b1101}: key_value <= 4'h8;  // 8
                            {2'b10, 4'b1011}: key_value <= 4'h9;  // 9
                            {2'b10, 4'b0111}: key_value <= 4'hC;  // C
                            {2'b11, 4'b1110}: key_value <= 4'hE;  // *->E
                            {2'b11, 4'b1101}: key_value <= 4'h0;  // 0
                            {2'b11, 4'b1011}: key_value <= 4'hF;  // #->F
                            {2'b11, 4'b0111}: key_value <= 4'hD;  // D
                            default: key_value <= 4'h0;
                        endcase
                    end
                end else begin
                    last_key_col <= keypad_col;
                    key_debounce_counter <= 4'h0;
                end
            end else begin
                key_valid <= 1'b0;
                key_debounce_counter <= 4'h0;
                last_key_col <= 4'hF;
            end

            key_scan_counter <= key_scan_counter + 1;
        end else begin
            key_valid <= 1'b0;  // 只在检测到按键时有效一个周期
        end
    end

    //==========================================================================
    // 主状态机
    //==========================================================================
    always @(posedge clk) begin
        if (reset) begin
            current_state <= IDLE;
            max_bot_num <= 10'd0;
            max_sgl_bot <= 10'd0;
            now_bot_bil_num <= 10'd0;
            bot_finished <= 10'd0;
            input_buffer <= 10'd0;
            error_flag <= 1'b0;
        end else begin
            case (current_state)
                IDLE: begin
                    if (start_btn_edge) begin
                        current_state <= SET_BOTTLE_NUM;
                        input_buffer <= 10'd0;
                        error_flag <= 1'b0;
                        bot_finished <= 10'd0;
                        now_bot_bil_num <= 10'd0;
                    end
                end

                SET_BOTTLE_NUM: begin
                    // 键盘输入处理
                    if (key_valid && key_value <= 4'h9) begin  // 只接受0-9
                        if (input_buffer <= 10'd99) begin  // 最大999，防止溢出
                            input_buffer <= input_buffer * 10 + key_value;
                        end
                    end

                    // 确认输入
                    if (confirm_btn_edge) begin
                        if (input_buffer > 0 && input_buffer <= 10'd999) begin
                            max_bot_num <= input_buffer;
                            input_buffer <= 10'd0;
                            current_state <= SET_PILL_NUM;
                        end else begin
                            error_flag <= 1'b1;  // 输入无效
                        end
                    end
                end

                SET_PILL_NUM: begin
                    // 键盘输入处理
                    if (key_valid && key_value <= 4'h9) begin  // 只接受0-9
                        if (input_buffer <= 10'd99) begin  // 最大999，防止溢出
                            input_buffer <= input_buffer * 10 + key_value;
                        end
                    end

                    // 确认输入
                    if (confirm_btn_edge) begin
                        if (input_buffer > 0 && input_buffer <= 10'd999) begin
                            max_sgl_bot <= input_buffer;
                            input_buffer <= 10'd0;
                            current_state <= WORKING;
                            now_bot_bil_num <= 10'd0;
                        end else begin
                            error_flag <= 1'b1;  // 输入无效
                        end
                    end
                end

                WORKING: begin
                    if (second_pulse) begin  // 每秒装一片药片
                        if (now_bot_bil_num < max_sgl_bot) begin
                            now_bot_bil_num <= now_bot_bil_num + 1;
                        end else begin
                            // 当前瓶装满
                            bot_finished <= bot_finished + 1;
                            now_bot_bil_num <= 10'd0;

                            // 检查是否完成所有瓶子
                            if (bot_finished + 1 >= max_bot_num) begin
                                current_state <= IDLE;  // 工作完成
                            end else if (mode_switch) begin
                                // 定制模式：暂停重新设置
                                current_state <= SET_PILL_NUM;
                                input_buffer <= 10'd0;
                            end
                            // 普通模式：继续下一瓶
                        end
                    end
                end

                default: current_state <= IDLE;
            endcase
        end
    end

    //==========================================================================
    // 显示数据处理
    //==========================================================================
    always @(posedge clk) begin
        if (reset) begin
            display_data <= 32'd0;
        end else begin
            case (current_state)
                IDLE: begin
                    display_data <= 32'd0;  // 显示 00000000
                end

                SET_BOTTLE_NUM: begin
                    display_data <= {22'd0, input_buffer};  // 显示输入的药瓶数
                end

                SET_PILL_NUM: begin
                    display_data <= {22'd0, input_buffer};  // 显示输入的药片数
                end

                WORKING: begin
                    if (display_switch) begin
                        // 显示实时工作状态：已完成瓶数(4位) + 当前瓶药片数(4位)
                        display_data <= {12'd0, bot_finished, 2'd0, now_bot_bil_num};
                    end else begin
                        // 显示配置参数：药瓶总数(4位) + 单瓶药片数(4位)
                        display_data <= {12'd0, max_bot_num, 2'd0, max_sgl_bot};
                    end
                end

                default: display_data <= 32'd0;
            endcase
        end
    end

    //==========================================================================
    // 数码管显示控制
    //==========================================================================
    always @(posedge clk) begin
        if (reset) begin
            nixie_counter <= 3'd0;
            nixie_enable <= 8'hFF;  // 全部关闭（低电平有效）
            nixie_segment <= 8'hFF; // 全部关闭（低电平有效）
        end else begin
            // 将32位数据分解为8个4位数字 (持续更新)
            digit[0] <= display_data[3:0];     // 最低位
            digit[1] <= display_data[7:4];
            digit[2] <= display_data[11:8];
            digit[3] <= display_data[15:12];
            digit[4] <= display_data[19:16];
            digit[5] <= display_data[23:20];
            digit[6] <= display_data[27:24];
            digit[7] <= display_data[31:28];   // 最高位

            // 使用更快的扫描频率 (每2^15个时钟周期 ≈ 0.33ms)
            if (ms_counter[14:0] == 15'd0) begin
                // 动态扫描显示
                nixie_enable <= ~(8'b00000001 << nixie_counter);  // 选中当前位（低电平有效）
                nixie_segment <= segment_code[digit[nixie_counter]]; // 显示对应数字
                nixie_counter <= nixie_counter + 1;
            end
        end
    end

    //==========================================================================
    // LED控制
    //==========================================================================
    always @(posedge clk) begin
        if (reset) begin
            red_led <= 8'h00;
            green_led <= 8'h00;
            yellow_led <= 8'h00;
            led_counter <= 27'd0;
            led_position <= 3'd0;
        end else begin
            // 红色LED：异常指示
            if (error_flag) begin
                red_led <= 8'hFF;  // 全亮表示异常
            end else begin
                red_led <= 8'h00;
            end

            // 黄色LED：状态指示
            case (current_state)
                IDLE: yellow_led <= 8'b00000000;        // 空闲：全灭
                SET_BOTTLE_NUM: yellow_led <= 8'b00000011; // 设置瓶数：11
                SET_PILL_NUM: yellow_led <= 8'b00000001;   // 设置药片数：01
                WORKING: yellow_led <= 8'b00000000;        // 工作中：00
                default: yellow_led <= 8'b00000000;
            endcase

            // 绿色LED：工作进度流水灯
            if (current_state == WORKING) begin
                // 0.5秒移动一次
                if (led_counter >= 27'd49_999_999) begin
                    led_counter <= 27'd0;
                    led_position <= led_position + 1;
                end else begin
                    led_counter <= led_counter + 1;
                end

                // 流水灯效果
                green_led <= 8'b00000001 << led_position;

                // 工作完成时全亮
                if (bot_finished >= max_bot_num) begin
                    green_led <= 8'hFF;
                end
            end else begin
                green_led <= 8'h00;
                led_counter <= 27'd0;
                led_position <= 3'd0;
            end
        end
    end

endmodule
