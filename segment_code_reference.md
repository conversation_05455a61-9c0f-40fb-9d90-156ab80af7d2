# 数码管段码表参考

## 🔍 问题分析

数码管显示不正确的主要原因是段码表定义与实际硬件不匹配。

### 数码管类型差异

#### 共阴极数码管
- 公共端接地（GND）
- 段信号高电平点亮
- 位选信号低电平有效

#### 共阳极数码管  
- 公共端接电源（VCC）
- 段信号低电平点亮
- 位选信号高电平有效

## 📋 段码表对比

### 原始错误的段码表（共阴极反向）
```verilog
segment_code[0] = 8'b11000000; // 0 - 错误
segment_code[1] = 8'b11111001; // 1 - 错误
segment_code[2] = 8'b10100100; // 2 - 错误
```

### 修正后的段码表（根据您的参考代码）
```verilog
segment_code[0] = 8'b00111111; // 0 - 正确
segment_code[1] = 8'b00000110; // 1 - 正确  
segment_code[2] = 8'b01011011; // 2 - 正确
```

## 🎯 完整的段码表

### 数字0-9的段码表
```verilog
// 格式: {DP,CG,CF,CE,CD,CC,CB,CA}
segment_code[0]  = 8'b00111111; // 0: 显示 ⬜⬜⬜
                                //    ⬜   ⬜
                                //    ⬜   ⬜  
                                //    ⬜⬜⬜

segment_code[1]  = 8'b00000110; // 1: 显示   ⬜
                                //      ⬜
                                //      ⬜
                                //      ⬜

segment_code[2]  = 8'b01011011; // 2: 显示 ⬜⬜⬜
                                //        ⬜
                                //    ⬜⬜⬜
                                //    ⬜
                                //    ⬜⬜⬜

segment_code[3]  = 8'b01001111; // 3: 显示 ⬜⬜⬜
                                //        ⬜
                                //      ⬜⬜⬜
                                //        ⬜
                                //    ⬜⬜⬜

segment_code[4]  = 8'b01100110; // 4: 显示 ⬜   ⬜
                                //    ⬜   ⬜
                                //    ⬜⬜⬜⬜
                                //        ⬜
                                //        ⬜

segment_code[5]  = 8'b01101101; // 5: 显示 ⬜⬜⬜
                                //    ⬜
                                //    ⬜⬜⬜
                                //        ⬜
                                //    ⬜⬜⬜

segment_code[6]  = 8'b01111101; // 6: 显示 ⬜⬜⬜
                                //    ⬜
                                //    ⬜⬜⬜
                                //    ⬜   ⬜
                                //    ⬜⬜⬜

segment_code[7]  = 8'b00000111; // 7: 显示 ⬜⬜⬜
                                //        ⬜
                                //        ⬜
                                //        ⬜
                                //        ⬜

segment_code[8]  = 8'b01111111; // 8: 显示 ⬜⬜⬜
                                //    ⬜   ⬜
                                //    ⬜⬜⬜
                                //    ⬜   ⬜
                                //    ⬜⬜⬜

segment_code[9]  = 8'b01101111; // 9: 显示 ⬜⬜⬜
                                //    ⬜   ⬜
                                //    ⬜⬜⬜
                                //        ⬜
                                //    ⬜⬜⬜
```

## 🔧 段位对应关系

### 标准七段数码管段位定义
```
     CA
   ------
CF |    | CB
   | CG |
   ------
CE |    | CC
   |    |
   ------  • DP
     CD
```

### 位序对应关系
```verilog
// nixie_segment[7:0] = {DP, CG, CF, CE, CD, CC, CB, CA}
//                      [0] [1] [2] [3] [4] [5] [6] [7]
```

### 引脚分配
```
CA -> F15 -> nixie_segment[7]
CB -> F13 -> nixie_segment[6]  
CC -> F14 -> nixie_segment[5]
CD -> F16 -> nixie_segment[4]
CE -> E17 -> nixie_segment[3]
CF -> C14 -> nixie_segment[2]
CG -> C15 -> nixie_segment[1]
DP -> E13 -> nixie_segment[0]
```

## ✅ 验证方法

### 1. 测试单个数字
```verilog
// 固定显示数字1
nixie_enable <= 8'b11111110;   // 选择第0个数码管
nixie_segment <= 8'b00000110;  // 显示数字1
```

### 2. 测试所有段
```verilog
// 全段点亮测试
nixie_enable <= 8'b11111110;   // 选择第0个数码管
nixie_segment <= 8'b01111111;  // 显示数字8（全段亮）
```

### 3. 逐段测试
```verilog
// 测试CA段
nixie_segment <= 8'b10000000;  // 只点亮CA段
// 测试CB段  
nixie_segment <= 8'b01000000;  // 只点亮CB段
// ... 依次测试每个段
```

## 🎯 修复结果

使用修正后的段码表，您应该能看到：

### 测试模块显示
- 数码管正确显示 "12345678"
- 每个数字清晰可见
- 无乱码或错误显示

### 主系统显示
- 初始状态显示 "00000000"
- 键盘输入正确显示
- 状态切换显示正常

## 📝 注意事项

1. **段码表必须与硬件匹配**：不同厂商的数码管可能有不同的段位定义
2. **位序很重要**：确保段码表的位序与引脚分配一致
3. **测试验证**：修改段码表后务必测试验证
4. **文档记录**：记录实际使用的段码表，便于后续维护

现在数码管应该能正确显示数字了！
