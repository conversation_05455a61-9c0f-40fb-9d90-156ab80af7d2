# Vivado项目创建脚本
# 用于创建药片装瓶系统项目

# 设置项目参数
set project_name "pill_bottle_system"
set project_dir "./vivado_project"
set part_name "xc7a100tfgg484-1"

# 创建项目
create_project $project_name $project_dir -part $part_name -force

# 添加源文件
add_files -norecurse {Top.v}

# 添加约束文件
add_files -fileset constrs_1 -norecurse {pill_bottle.xdc}

# 添加仿真文件
add_files -fileset sim_1 -norecurse {pill_bottle_tb.v}

# 设置顶层模块
set_property top pill_bottle_system [current_fileset]

# 更新编译顺序
update_compile_order -fileset sources_1
update_compile_order -fileset sim_1

# 设置仿真顶层
set_property top pill_bottle_tb [get_filesets sim_1]

puts "项目创建完成！"
puts "项目位置: $project_dir/$project_name.xpr"
puts "下一步："
puts "1. 运行综合: synth_design"
puts "2. 运行实现: opt_design, place_design, route_design"
puts "3. 生成比特流: write_bitstream"
puts "4. 或者运行仿真: launch_simulation"
