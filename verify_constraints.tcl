# 约束验证脚本
# 专门用于验证数码管段选约束是否正确

puts "=== 约束验证脚本 ==="

# 检查约束文件是否存在
if {![file exists "pill_bottle.xdc"]} {
    puts "错误: 约束文件 pill_bottle.xdc 不存在"
    return
}

puts "约束文件存在: pill_bottle.xdc"

# 读取约束文件内容
set fp [open "pill_bottle.xdc" r]
set content [read $fp]
close $fp

# 检查数码管段选约束
puts "\n=== 检查数码管段选约束 ==="
set segment_pins {F15 F13 F14 F16 E17 C14 C15 E13}
set segment_names {CA CB CC CD CE CF CG DP}

for {set i 0} {$i < 8} {incr i} {
    set pin [lindex $segment_pins $i]
    set name [lindex $segment_names $i]
    set pattern "nixie_segment\\\[$i\\\].*$pin"
    
    if {[regexp $pattern $content]} {
        puts "  ✓ nixie_segment\[$i\] ($name) -> $pin"
    } else {
        puts "  ✗ nixie_segment\[$i\] ($name) -> $pin 约束缺失"
    }
}

# 检查数码管位选约束
puts "\n=== 检查数码管位选约束 ==="
set enable_pins {A18 A20 B20 E18 F18 D19 E19 C19}

for {set i 0} {$i < 8} {incr i} {
    set pin [lindex $enable_pins $i]
    set pattern "nixie_enable\\\[$i\\\].*$pin"
    
    if {[regexp $pattern $content]} {
        puts "  ✓ nixie_enable\[$i\] (A$i) -> $pin"
    } else {
        puts "  ✗ nixie_enable\[$i\] (A$i) -> $pin 约束缺失"
    }
}

# 检查IOSTANDARD约束
puts "\n=== 检查IOSTANDARD约束 ==="
if {[regexp {IOSTANDARD LVCMOS33.*nixie_segment} $content]} {
    puts "  ✓ nixie_segment IOSTANDARD 约束存在"
} else {
    puts "  ✗ nixie_segment IOSTANDARD 约束缺失"
}

if {[regexp {IOSTANDARD LVCMOS33.*nixie_enable} $content]} {
    puts "  ✓ nixie_enable IOSTANDARD 约束存在"
} else {
    puts "  ✗ nixie_enable IOSTANDARD 约束缺失"
}

puts "\n=== 验证完成 ==="

# 生成修复建议
puts "\n=== 修复建议 ==="
puts "如果仍然出现UCIO-1错误，请尝试："
puts "1. 确保约束文件已正确添加到项目中"
puts "2. 检查顶层模块名称是否为 pill_bottle_system"
puts "3. 重新运行综合: synth_design -top pill_bottle_system"
puts "4. 检查端口名称是否与Verilog代码完全匹配"
