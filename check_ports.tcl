# 端口检查脚本
# 用于检查所有端口是否都有约束

puts "=== 端口约束检查 ==="

# 获取所有端口
set all_ports [get_ports]
puts "总端口数: [llength $all_ports]"

# 检查未约束的端口
set unconstrained_ports [get_ports -filter {LOC == ""}]
puts "未约束端口数: [llength $unconstrained_ports]"

if {[llength $unconstrained_ports] > 0} {
    puts "\n未约束的端口:"
    foreach port $unconstrained_ports {
        puts "  - $port"
    }
} else {
    puts "所有端口都已约束"
}

# 检查数码管段选端口
puts "\n=== 数码管段选端口检查 ==="
for {set i 0} {$i < 8} {incr i} {
    set port_name "nixie_segment\[$i\]"
    if {[llength [get_ports $port_name -quiet]] > 0} {
        set port [get_ports $port_name]
        set loc [get_property LOC $port]
        set iostd [get_property IOSTANDARD $port]
        puts "  $port_name -> LOC: $loc, IOSTANDARD: $iostd"
    } else {
        puts "  $port_name -> 端口不存在"
    }
}

# 检查数码管位选端口
puts "\n=== 数码管位选端口检查 ==="
for {set i 0} {$i < 8} {incr i} {
    set port_name "nixie_enable\[$i\]"
    if {[llength [get_ports $port_name -quiet]] > 0} {
        set port [get_ports $port_name]
        set loc [get_property LOC $port]
        set iostd [get_property IOSTANDARD $port]
        puts "  $port_name -> LOC: $loc, IOSTANDARD: $iostd"
    } else {
        puts "  $port_name -> 端口不存在"
    }
}

puts "\n=== 检查完成 ==="
