# 数码管测试约束文件
# 简化版本，只包含必要的数码管和LED约束

#==============================================================================
# 时钟约束
#==============================================================================
set_property PACKAGE_PIN Y18 [get_ports clk]
set_property IOSTANDARD LVCMOS33 [get_ports clk]
create_clock -add -name sys_clk_pin -period 10.00 -waveform {0 5} [get_ports clk]

#==============================================================================
# 复位约束
#==============================================================================
set_property PACKAGE_PIN P20 [get_ports reset]
set_property IOSTANDARD LVCMOS33 [get_ports reset]

#==============================================================================
# 数码管约束
#==============================================================================
# 数码管位选 A7-A0 (低电平有效)
set_property PACKAGE_PIN A18 [get_ports nixie_enable[7]]
set_property PACKAGE_PIN A20 [get_ports nixie_enable[6]]
set_property PACKAGE_PIN B20 [get_ports nixie_enable[5]]
set_property PACKAGE_PIN E18 [get_ports nixie_enable[4]]
set_property PACKAGE_PIN F18 [get_ports nixie_enable[3]]
set_property PACKAGE_PIN D19 [get_ports nixie_enable[2]]
set_property PACKAGE_PIN E19 [get_ports nixie_enable[1]]
set_property PACKAGE_PIN C19 [get_ports nixie_enable[0]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_enable[7]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_enable[6]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_enable[5]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_enable[4]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_enable[3]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_enable[2]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_enable[1]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_enable[0]]

# 数码管段选 CA-CG,DP (低电平有效)
set_property PACKAGE_PIN F15 [get_ports nixie_segment[7]]  # CA
set_property PACKAGE_PIN F13 [get_ports nixie_segment[6]]  # CB
set_property PACKAGE_PIN F14 [get_ports nixie_segment[5]]  # CC
set_property PACKAGE_PIN F16 [get_ports nixie_segment[4]]  # CD
set_property PACKAGE_PIN E17 [get_ports nixie_segment[3]]  # CE
set_property PACKAGE_PIN C14 [get_ports nixie_segment[2]]  # CF
set_property PACKAGE_PIN C15 [get_ports nixie_segment[1]]  # CG
set_property PACKAGE_PIN E13 [get_ports nixie_segment[0]]  # DP
set_property IOSTANDARD LVCMOS33 [get_ports nixie_segment[7]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_segment[6]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_segment[5]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_segment[4]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_segment[3]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_segment[2]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_segment[1]]
set_property IOSTANDARD LVCMOS33 [get_ports nixie_segment[0]]

#==============================================================================
# LED约束 (用于指示系统状态)
#==============================================================================
# 红色LED
set_property PACKAGE_PIN N19 [get_ports red_led[0]]
set_property PACKAGE_PIN N20 [get_ports red_led[1]]
set_property PACKAGE_PIN M20 [get_ports red_led[2]]
set_property PACKAGE_PIN K13 [get_ports red_led[3]]
set_property PACKAGE_PIN K14 [get_ports red_led[4]]
set_property PACKAGE_PIN M13 [get_ports red_led[5]]
set_property PACKAGE_PIN L13 [get_ports red_led[6]]
set_property PACKAGE_PIN K17 [get_ports red_led[7]]
set_property IOSTANDARD LVCMOS33 [get_ports red_led[*]]

# 绿色LED
set_property PACKAGE_PIN J17 [get_ports green_led[0]]
set_property PACKAGE_PIN L14 [get_ports green_led[1]]
set_property PACKAGE_PIN L15 [get_ports green_led[2]]
set_property PACKAGE_PIN L16 [get_ports green_led[3]]
set_property PACKAGE_PIN K16 [get_ports green_led[4]]
set_property PACKAGE_PIN M15 [get_ports green_led[5]]
set_property PACKAGE_PIN M16 [get_ports green_led[6]]
set_property PACKAGE_PIN M17 [get_ports green_led[7]]
set_property IOSTANDARD LVCMOS33 [get_ports green_led[*]]

# 黄色LED
set_property PACKAGE_PIN A21 [get_ports yellow_led[0]]
set_property PACKAGE_PIN E22 [get_ports yellow_led[1]]
set_property PACKAGE_PIN D22 [get_ports yellow_led[2]]
set_property PACKAGE_PIN E21 [get_ports yellow_led[3]]
set_property PACKAGE_PIN D21 [get_ports yellow_led[4]]
set_property PACKAGE_PIN G21 [get_ports yellow_led[5]]
set_property PACKAGE_PIN G22 [get_ports yellow_led[6]]
set_property PACKAGE_PIN F21 [get_ports yellow_led[7]]
set_property IOSTANDARD LVCMOS33 [get_ports yellow_led[*]]

#==============================================================================
# 时序约束
#==============================================================================
set_false_path -from [get_ports reset]
