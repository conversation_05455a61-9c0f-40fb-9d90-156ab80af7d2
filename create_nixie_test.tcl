# 数码管测试项目创建脚本
# 用于快速创建和测试数码管显示功能

# 设置项目参数
set project_name "nixie_test"
set project_dir "./nixie_test_project"
set part_name "xc7a100tfgg484-1"

puts "=== 创建数码管测试项目 ==="

# 创建项目
create_project $project_name $project_dir -part $part_name -force

# 添加源文件
add_files -norecurse {nixie_test.v}

# 添加约束文件
add_files -fileset constrs_1 -norecurse {nixie_test.xdc}

# 设置顶层模块
set_property top nixie_test_top [current_fileset]

# 更新编译顺序
update_compile_order -fileset sources_1

puts "项目创建完成！"
puts "项目位置: $project_dir/$project_name.xpr"

# 运行综合
puts "\n正在运行综合..."
if {[catch {synth_design -top nixie_test_top} result]} {
    puts "综合失败: $result"
    return
} else {
    puts "综合成功！"
}

# 检查DRC
puts "\n运行DRC检查..."
if {[catch {report_drc -file nixie_test_drc.txt} drc_result]} {
    puts "DRC检查失败: $drc_result"
} else {
    puts "DRC检查完成，结果保存在 nixie_test_drc.txt"
}

# 运行实现
puts "\n正在运行实现..."
if {[catch {
    opt_design
    place_design
    route_design
} impl_result]} {
    puts "实现失败: $impl_result"
    return
} else {
    puts "实现成功！"
}

# 生成比特流
puts "\n正在生成比特流..."
if {[catch {write_bitstream -force nixie_test.bit} bit_result]} {
    puts "比特流生成失败: $bit_result"
} else {
    puts "比特流生成成功: nixie_test.bit"
}

puts "\n=== 测试项目完成 ==="
puts "下载比特流到FPGA后，应该看到："
puts "1. 数码管显示 '12345678'"
puts "2. 黄色LED每秒闪烁一次"
puts "3. 绿色LED显示流水灯效果"
puts "4. 红色LED全部熄灭"
puts "\n如果数码管不显示，请检查硬件连接和电源。"
